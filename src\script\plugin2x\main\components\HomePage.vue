<!-- eslint-disable max-depth -->
<template>
    <div class="home-page">
        <!-- 侧边栏组件 -->
        <Sidebar
            :menu-groups="menuGroups"
            :active-menu-item="activeMenuItem"
            :is-ai-responding="isAiResponding"
            :is-stream-response="isStreamResponse"
            @new-chat="handleNewChat"
            @search-select="handleSearchSelect"
            @menu-item-click="handleMenuItemClick"
            @delete-session="handleDeleteSession"
            @stop-generation="handleStopGeneration"
        />

        <!-- 主内容区域组件 -->
        <MainContent
            v-if="currentView === 'home'"
            :welcome-config="welcomeConfig"
            :input-config="inputConfig"
            :feature-cards="featureCards"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            @card-click="handleCardClick"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
        />

        <!-- 聊天页面组件（预留） -->
        <ChatPage
            v-if="currentView === 'chat'"
            :title="currentSessionTitle"
            :session-id="currentSessionId"
            :messages="chatMessages"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            :is-stream-response="isStreamResponse"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
            @stop-generation="handleStopGeneration"
            @update-title="handleUpdateSessionTitle"
        />
    </div>
</template>

<script>
import Sidebar from '@/script/components/Sidebar.vue';
import MainContent from '@/script/components/MainContent.vue';
import ChatPage from '@/script/components/ChatPage.vue';
import AssistantApi from '@/script/api/module/assistant.js';
import chatStorageService from '@/script/utils/chatStorage.js';
import AIConversationManager from '@/script/utils/AIConversationManager.js';

// 配置常量
const CONFIG = {
    // 视图类型
    VIEWS: {
        HOME: 'home',
        CHAT: 'chat'
    },

    // 时间常量（毫秒）
    TIME_PERIODS: {
        DAY: 24 * 60 * 60 * 1000,
        WEEK: 7 * 24 * 60 * 60 * 1000,
        MONTH: 30 * 24 * 60 * 60 * 1000
    },

    // 会话分组配置
    SESSION_GROUPS: {
        today: { key: 'today', title: '今天' },
        yesterday: { key: 'yesterday', title: '昨天' },
        week: { key: 'week', title: '一周内' },
        month: { key: 'month', title: '30天内' }
    },

    // 消息类型
    MESSAGE_TYPES: {
        USER: 'user',
        ASSISTANT: 'assistant',
        SYSTEM: 'system'
    },

    // 消息动作类型
    MESSAGE_ACTIONS: {
        ADD: 'add',
        UPDATE: 'update',
        COMPLETE: 'complete',
        STOPPED: 'stopped',
        ERROR: 'error',
        REMOVED: 'removed'
    },

    // 临时会话前缀
    TEMP_SESSION_PREFIX: 'temp_',

    // 默认模型
    DEFAULT_MODEL: 'DifyStream'
};

export default {
    name: 'HomePage',
    components: {
        Sidebar,
        MainContent,
        ChatPage
    },
    data() {
        return {
            currentView: CONFIG.VIEWS.HOME,
            activeMenuItem: null,
            chatMessages: [], // 聊天消息列表
            selectedModel: CONFIG.DEFAULT_MODEL, // 当前选择的模型

            // 会话管理相关状态
            currentSessionId: null, // 当前会话ID
            chatSessions: [], // 聊天会话列表
            isStorageAvailable: false, // 存储服务是否可用
            isTemporarySession: false, // 当前是否为临时会话（未保存）

            // AI会话管理器
            aiConversationManager: null,

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            },

            inputConfig: {
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            },
            modelOptions: [
                {
                    label: 'Dify',
                    value: CONFIG.DEFAULT_MODEL,
                    desc: '接入数据资产知识库'
                }
            ],

            featureCards: [
                {
                    id: 'location-capability',
                    bg: require('@/img/main/card-bg-1.png'),
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据用户需求及业务口径，结合开放目录、区域洞察AP1、实时事件、平台指标，将匹配的能力以不同的组件形式向用户展示。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    bg: require('@/img/main/card-bg-2.png'),
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订购向导',
                    description:
                        '分析用户需求，引导用户订购推荐的数据资产(API/数据开放目录表等)，同时输出可视化操作步骤指引用户订购。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    bg: require('@/img/main/card-bg-3.png'),
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求规格',
                    description:
                        '提取用户需求中的时空信息，包括业务背景、业务口径、数据账期地理范围，以格式化方式呈现，支持生成并下载需求规格说明书。',
                    action: 'business-data'
                }
            ]
        };
    },

    computed: {
        // 默认模型
        defaultModel() {
            return this.modelOptions[0].value;
        },
        // 动态生成菜单组
        menuGroups() {
            if (!this.isStorageAvailable || this.chatSessions.length === 0) {
                return [];
            }

            return this.groupSessionsByTime(this.chatSessions);
        },

        // 当前会话标题
        currentSessionTitle() {
            if (!this.currentSessionId || !this.isStorageAvailable) {
                return '新对话';
            }

            // 如果是临时会话，直接返回"新对话"
            if (this.isTemporarySession) {
                return '新对话';
            }

            const session = this.chatSessions.find((s) => s.id === this.currentSessionId);
            if (session) {
                return session.title;
            }
            return '新对话';
        },

        // AI状态相关计算属性
        isAiResponding() {
            if (this.aiConversationManager) {
                return this.aiConversationManager.getState().isAiResponding;
            }
            return false;
        },

        isStreamResponse() {
            if (this.aiConversationManager) {
                return this.aiConversationManager.getState().isStreamResponse;
            }
            return false;
        }
    },

    async mounted() {
        // 初始化聊天存储服务
        this.isStorageAvailable = chatStorageService.init();

        // 初始化AI会话管理器
        this.aiConversationManager = new AIConversationManager({
            assistantApi: AssistantApi,
            chatStorageService: chatStorageService,
            modelOptions: this.modelOptions,
            defaultModel: this.defaultModel,
            onStateChange: this.handleAiStateChange,
            onMessageUpdate: this.handleAiMessageUpdate,
            onSessionUpdate: this.handleAiSessionUpdate
        });

        if (this.isStorageAvailable) {
            // 加载聊天会话列表
            await this.loadChatSessions();

            // 恢复当前会话
            await this.restoreCurrentSession();
        }
    },

    methods: {
        // 处理新对话
        async handleNewChat() {
            // 如果AI正在回复，禁用新建对话功能
            if (this.isAiResponding) {
                return;
            }

            // 清理当前的临时会话（如果存在且为空）
            await this.cleanupEmptyTemporarySession();

            // 创建临时会话（不保存到存储）
            this.currentSessionId = this.generateTempSessionId();
            this.chatMessages = [];
            this.activeMenuItem = this.currentSessionId;
            this.isTemporarySession = true;

            this.currentView = 'home';
        },

        // 加载聊天会话列表
        async loadChatSessions() {
            if (!this.isStorageAvailable) return;

            try {
                this.chatSessions = chatStorageService.getAllSessions();
            } catch (error) {
                console.error('加载聊天会话失败:', error);
                this.chatSessions = [];
            }
        },

        // 恢复当前会话
        async restoreCurrentSession() {
            if (!this.isStorageAvailable) return;

            try {
                const currentSessionId = chatStorageService.getCurrentSessionId();
                if (!currentSessionId) return;

                // 检查是否为临时会话ID，如果是则不恢复
                if (currentSessionId.startsWith('temp_')) {
                    chatStorageService.setCurrentSessionId(null);
                    return;
                }

                const session = chatStorageService.getSession(currentSessionId);
                if (!session) return;

                this.currentSessionId = currentSessionId;
                if (session.messages) {
                    this.chatMessages = session.messages;
                } else {
                    this.chatMessages = [];
                }
                if (session.model && this.modelOptions.find((m) => m.value === session.model)) {
                    this.selectedModel = session.model;
                } else {
                    this.selectedModel = this.defaultModel;
                }
                this.activeMenuItem = currentSessionId;
                this.isTemporarySession = false;

                // 如果有消息，切换到聊天页面
                if (this.chatMessages.length > 0) {
                    this.currentView = 'chat';
                }
            } catch (error) {
                console.error('恢复当前会话失败:', error);
            }
        },

        // 按时间分组会话
        groupSessionsByTime(sessions) {
            const timeRanges = this.createTimeRanges();
            const groups = this.initializeGroups();

            sessions.forEach((session) => {
                const sessionDate = new Date(session.updatedAt);
                const item = this.createSessionItem(session);
                const groupKey = this.determineGroupKey(sessionDate, timeRanges);

                if (groupKey && groups[groupKey]) {
                    groups[groupKey].items.push(item);
                }
            });

            // 只返回有内容的分组
            return Object.values(groups).filter((group) => group.items.length > 0);
        },

        // 创建时间范围
        createTimeRanges() {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            return {
                today: today,
                yesterday: new Date(today.getTime() - CONFIG.TIME_PERIODS.DAY),
                weekAgo: new Date(today.getTime() - CONFIG.TIME_PERIODS.WEEK),
                monthAgo: new Date(today.getTime() - CONFIG.TIME_PERIODS.MONTH)
            };
        },

        // 初始化分组
        initializeGroups() {
            const groups = {};
            Object.keys(CONFIG.SESSION_GROUPS).forEach((key) => {
                groups[key] = {
                    ...CONFIG.SESSION_GROUPS[key],
                    items: []
                };
            });
            return groups;
        },

        // 创建会话项
        createSessionItem(session) {
            return {
                id: session.id,
                text: session.title,
                type: 'conversation',
                timestamp: session.updatedAt,
                messageCount: session.messageCount || 0
            };
        },

        // 确定分组键
        determineGroupKey(sessionDate, timeRanges) {
            if (sessionDate >= timeRanges.today) {
                return 'today';
            }
            if (sessionDate >= timeRanges.yesterday) {
                return 'yesterday';
            }
            if (sessionDate >= timeRanges.weekAgo) {
                return 'week';
            }
            if (sessionDate >= timeRanges.monthAgo) {
                return 'month';
            }
            return null;
        },

        // 处理搜索选择
        async handleSearchSelect(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponding) {
                return;
            }

            await this.switchToSession(item.id);
        },

        // 处理菜单项点击
        async handleMenuItemClick(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponding) {
                return;
            }

            if (item.type === 'conversation') {
                await this.switchToSession(item.id);
            }
        },

        // 切换到指定会话
        async switchToSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 清理当前的临时会话（如果存在且为空）
                await this.cleanupEmptyTemporarySession();

                const session = chatStorageService.getSession(sessionId);
                if (session) {
                    this.currentSessionId = sessionId;
                    if (session.messages) {
                        this.chatMessages = session.messages;
                    } else {
                        this.chatMessages = [];
                    }
                    if (session.model && this.modelOptions.find((m) => m.value === session.model)) {
                        this.selectedModel = session.model;
                    } else {
                        this.selectedModel = this.defaultModel;
                    }
                    this.activeMenuItem = sessionId;
                    this.currentView = 'chat';
                    this.isTemporarySession = false;

                    // 更新当前会话ID
                    chatStorageService.setCurrentSessionId(sessionId);
                }
            } catch (error) {
                console.error('切换会话失败:', error);
            }
        },

        // 生成临时会话ID
        generateTempSessionId() {
            return 'temp_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
        },

        // 清理空的临时会话
        async cleanupEmptyTemporarySession() {
            if (this.isTemporarySession && this.chatMessages.length === 0) {
                // 如果当前是空的临时会话，清理状态
                this.currentSessionId = null;
                this.activeMenuItem = null;
                this.isTemporarySession = false;

                // 清除localStorage中的当前会话ID
                if (this.isStorageAvailable) {
                    chatStorageService.setCurrentSessionId(null);
                }
            }
        },

        // 处理删除会话
        async handleDeleteSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 删除会话
                const success = chatStorageService.deleteSession(sessionId);
                if (success) {
                    // 重新加载会话列表
                    await this.loadChatSessions();

                    // 如果删除的是当前会话，切换到首页
                    if (this.currentSessionId === sessionId) {
                        this.currentSessionId = null;
                        this.chatMessages = [];
                        this.activeMenuItem = null;
                        this.currentView = 'home';
                    }
                }
            } catch (error) {
                console.error('删除会话失败:', error);
                this.$message.error('删除对话失败，请重试');
            }
        },

        // 处理功能卡片点击
        async handleCardClick() {
            // 创建新会话并切换到聊天页面
            await this.handleNewChat();
        },

        // 处理发送消息
        async handleSendMessage(message) {
            // 如果正在回复中，不允许发送新消息
            if (this.isAiResponding) {
                return;
            }

            // 确保有当前会话
            await this.ensureCurrentSession(message.model);

            // 添加用户消息到聊天记录
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.chatMessages.push(userMessage);

            // 保存用户消息到存储
            if (this.isStorageAvailable && this.currentSessionId) {
                const saveSuccess = chatStorageService.addMessage(
                    this.currentSessionId,
                    userMessage
                );
                if (saveSuccess) {
                    // 重新加载会话列表以更新侧边栏
                    await this.loadChatSessions();
                } else {
                    console.warn('保存用户消息失败');
                }
            }

            // 切换到聊天页面
            this.currentView = 'chat';

            // 使用AI会话管理器发送消息
            try {
                const aiResponse = await this.aiConversationManager.sendMessage(
                    message,
                    this.currentSessionId,
                    this.chatMessages
                );

                // 只有非流式响应才需要手动添加到消息列表
                // 流式响应通过回调已经添加到消息列表中
                if (aiResponse) {
                    // 添加AI回复到聊天记录
                    this.chatMessages.push(aiResponse);

                    // 保存AI回复到存储
                    if (this.isStorageAvailable && this.currentSessionId) {
                        chatStorageService.addMessage(this.currentSessionId, aiResponse);
                        // 重新加载会话列表以更新侧边栏
                        await this.loadChatSessions();
                    }
                }
            } catch (error) {
                console.error('AI消息发送失败:', error);

                // 检查是否是用户主动取消的请求
                if (error && error.isCancelled) {
                    // 用户取消了请求，清理可能的空消息
                    this.aiConversationManager.cleanupEmptyAiMessage(
                        this.chatMessages,
                        this.currentSessionId
                    );
                } else {
                    // 清理可能的空消息（在API错误的情况下）
                    this.aiConversationManager.cleanupEmptyAiMessage(
                        this.chatMessages,
                        this.currentSessionId
                    );

                    // 添加错误消息
                    const errorResponse = {
                        text: `抱歉，服务暂时不可用。错误信息：${error.message || '未知错误'}`,
                        type: 'assistant',
                        timestamp: new Date().toISOString(),
                        isError: true
                    };
                    this.chatMessages.push(errorResponse);

                    // 保存错误消息到存储
                    if (this.isStorageAvailable && this.currentSessionId) {
                        chatStorageService.addMessage(this.currentSessionId, errorResponse);
                        // 重新加载会话列表以更新侧边栏
                        this.loadChatSessions();
                    }
                }
            }
        },

        // 处理停止生成
        handleStopGeneration() {
            if (!this.aiConversationManager) {
                return;
            }

            const stopped = this.aiConversationManager.stopGeneration();
            if (!stopped) {
                return;
            }

            // 处理被停止的AI消息
            const result = this.aiConversationManager.handleStoppedAiMessage(
                this.chatMessages,
                this.currentSessionId
            );

            if (!result.success) {
                return;
            }

            // 处理被移除的消息
            this.handleRemovedMessage(result.processedMessage);

            // 添加停止消息到本地消息列表
            if (result.stopMessage) {
                this.chatMessages.push(result.stopMessage);
            }

            // 重新加载会话列表以更新侧边栏
            if (this.isStorageAvailable) {
                this.loadChatSessions();
            }
        },

        // 处理被移除的消息
        handleRemovedMessage(processedMessage) {
            if (!processedMessage || processedMessage.action !== 'removed') {
                return;
            }

            const messageIndex = this.chatMessages.findIndex(
                (msg) => msg === processedMessage.message
            );
            if (messageIndex !== -1) {
                this.chatMessages.splice(messageIndex, 1);
            }
        },

        // AI状态变化回调
        handleAiStateChange(newState, oldState) {
            // 强制更新组件以反映状态变化
            this.$forceUpdate();
        },

        // AI消息更新回调
        handleAiMessageUpdate(message, action) {
            // 消息动作处理映射
            const actionHandlers = {
                [CONFIG.MESSAGE_ACTIONS.ADD]: () => {
                    this.chatMessages.push(message);
                },
                [CONFIG.MESSAGE_ACTIONS.COMPLETE]: () => {
                    this.saveMessageToStorage(message);
                },
                [CONFIG.MESSAGE_ACTIONS.UPDATE]: () => {
                    // 流式更新处理
                },
                [CONFIG.MESSAGE_ACTIONS.STOPPED]: () => {
                    // 消息被停止处理
                },
                [CONFIG.MESSAGE_ACTIONS.ERROR]: () => {
                    // 消息错误处理
                }
            };

            const handler = actionHandlers[action];
            if (handler) {
                handler();
                this.forceComponentUpdate();
            }
        },

        // 保存消息到存储
        saveMessageToStorage(message) {
            if (this.isStorageAvailable && this.currentSessionId) {
                chatStorageService.addMessage(this.currentSessionId, message);
                this.loadChatSessions();
            }
        },

        // 强制组件更新
        forceComponentUpdate() {
            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },

        // AI会话更新回调
        handleAiSessionUpdate(sessionId) {
            // 重新加载会话列表以更新侧边栏
            if (this.isStorageAvailable) {
                this.loadChatSessions();
            }
        },

        // 确保有当前会话
        async ensureCurrentSession(model) {
            if (!this.isStorageAvailable) return;

            if (!this.currentSessionId || this.isTemporarySession) {
                // 如果是临时会话，转为正式会话
                if (this.isTemporarySession) {
                    const newSession = chatStorageService.createSession({
                        model: model || this.selectedModel
                    });

                    this.currentSessionId = newSession.id;
                    this.activeMenuItem = newSession.id;
                    this.isTemporarySession = false;
                } else {
                    // 创建新会话
                    const newSession = chatStorageService.createSession({
                        model: model || this.selectedModel
                    });

                    this.currentSessionId = newSession.id;
                    this.activeMenuItem = newSession.id;
                }

                // 重新加载会话列表
                await this.loadChatSessions();
            }
        },

        // 处理模型变更
        async handleModelChange(newModel) {
            this.selectedModel = newModel;

            // 使用AI会话管理器更改模型
            if (this.aiConversationManager) {
                await this.aiConversationManager.changeModel(newModel, this.currentSessionId);
            }
        },

        // 处理会话标题更新
        async handleUpdateSessionTitle(sessionId, newTitle) {
            if (!this.isStorageAvailable || !sessionId || !newTitle) return;

            // 不允许更新临时会话的标题
            if (sessionId.startsWith('temp_')) {
                console.warn('不能更新临时会话的标题');
                return;
            }

            try {
                const success = chatStorageService.updateSessionTitle(sessionId, newTitle);
                if (success) {
                    // 重新加载会话列表以更新侧边栏
                    await this.loadChatSessions();
                }
            } catch (error) {
                console.error('更新会话标题失败:', error);
                this.$message.error('更新标题失败，请重试');
            }
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100vh;
}
</style>
