<!-- eslint-disable max-depth -->
<template>
    <div class="home-page">
        <!-- 侧边栏组件 -->
        <Sidebar
            :menu-groups="menuGroups"
            :active-menu-item="activeMenuItem"
            :is-ai-responding="isAiResponding"
            :is-stream-response="isStreamResponse"
            @new-chat="handleNewChat"
            @search-select="handleSearchSelect"
            @menu-item-click="handleMenuItemClick"
            @delete-session="handleDeleteSession"
            @stop-generation="handleStopGeneration"
        />

        <!-- 主内容区域组件 -->
        <MainContent
            v-if="currentView === 'home'"
            :welcome-config="welcomeConfig"
            :input-config="inputConfig"
            :feature-cards="featureCards"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            @card-click="handleCardClick"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
        />

        <!-- 聊天页面组件（预留） -->
        <ChatPage
            v-if="currentView === 'chat'"
            :title="currentSessionTitle"
            :session-id="currentSessionId"
            :messages="chatMessages"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            :is-stream-response="isStreamResponse"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
            @stop-generation="handleStopGeneration"
            @update-title="handleUpdateSessionTitle"
        />
    </div>
</template>

<script>
import Sidebar from '@/script/components/Sidebar.vue';
import MainContent from '@/script/components/MainContent.vue';
import ChatPage from '@/script/components/ChatPage.vue';
import AssistantApi from '@/script/api/module/assistant.js';
import chatStorageService from '@/script/utils/chatStorage.js';
import AIConversationManager from '@/script/utils/AIConversationManager.js';
import SessionTimeGroupManager from '@/script/utils/SessionTimeGroupManager.js';

// 配置常量
const CONFIG = {
    // 视图类型
    VIEWS: {
        HOME: 'home',
        CHAT: 'chat'
    },

    // 时间常量（毫秒）
    TIME_PERIODS: {
        DAY: 24 * 60 * 60 * 1000,
        WEEK: 7 * 24 * 60 * 60 * 1000,
        MONTH: 30 * 24 * 60 * 60 * 1000
    },

    // 会话分组配置
    SESSION_GROUPS: {
        today: { key: 'today', title: '今天' },
        yesterday: { key: 'yesterday', title: '昨天' },
        week: { key: 'week', title: '一周内' },
        month: { key: 'month', title: '30天内' }
    },

    // 消息类型
    MESSAGE_TYPES: {
        USER: 'user',
        ASSISTANT: 'assistant',
        SYSTEM: 'system'
    },

    // 消息动作类型
    MESSAGE_ACTIONS: {
        ADD: 'add',
        UPDATE: 'update',
        COMPLETE: 'complete',
        STOPPED: 'stopped',
        ERROR: 'error',
        REMOVED: 'removed'
    },

    // 临时会话前缀
    TEMP_SESSION_PREFIX: 'temp_',

    // 默认模型
    DEFAULT_MODEL: 'DifyStream'
};

export default {
    name: 'HomePage',
    components: {
        Sidebar,
        MainContent,
        ChatPage
    },
    data() {
        return {
            currentView: CONFIG.VIEWS.HOME,
            activeMenuItem: null,
            chatMessages: [], // 聊天消息列表
            selectedModel: CONFIG.DEFAULT_MODEL, // 当前选择的模型

            // 会话管理相关状态
            currentSessionId: null, // 当前会话ID
            chatSessions: [], // 聊天会话列表
            isStorageAvailable: false, // 存储服务是否可用
            isTemporarySession: false, // 当前是否为临时会话（未保存）

            // AI会话管理器
            aiConversationManager: null,

            // 时间分组管理器
            timeGroupManager: null,

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            },

            inputConfig: {
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            },
            modelOptions: [
                {
                    label: 'Dify',
                    value: CONFIG.DEFAULT_MODEL,
                    desc: '接入数据资产知识库'
                }
            ],

            featureCards: this.createFeatureCards()
        };
    },

    computed: {
        // 默认模型
        defaultModel() {
            return this.modelOptions[0].value;
        },
        // 动态生成菜单组
        menuGroups() {
            if (!this.isStorageAvailable || this.chatSessions.length === 0) {
                return [];
            }

            return this.groupSessionsByTime(this.chatSessions);
        },

        // 当前会话标题
        currentSessionTitle() {
            if (!this.currentSessionId || !this.isStorageAvailable) {
                return '新对话';
            }

            // 如果是临时会话，直接返回"新对话"
            if (this.isTemporarySession) {
                return '新对话';
            }

            const session = this.chatSessions.find((s) => s.id === this.currentSessionId);
            if (session) {
                return session.title;
            }
            return '新对话';
        },

        // AI状态相关计算属性
        isAiResponding() {
            if (this.aiConversationManager) {
                return this.aiConversationManager.getState().isAiResponding;
            }
            return false;
        },

        isStreamResponse() {
            if (this.aiConversationManager) {
                return this.aiConversationManager.getState().isStreamResponse;
            }
            return false;
        }
    },

    async mounted() {
        // 初始化聊天存储服务
        this.isStorageAvailable = chatStorageService.init();

        // 初始化AI会话管理器
        this.aiConversationManager = new AIConversationManager({
            assistantApi: AssistantApi,
            chatStorageService: chatStorageService,
            modelOptions: this.modelOptions,
            defaultModel: this.defaultModel,
            onStateChange: this.handleAiStateChange,
            onMessageUpdate: this.handleAiMessageUpdate,
            onSessionUpdate: this.handleAiSessionUpdate
        });

        // 初始化时间分组管理器
        this.timeGroupManager = new SessionTimeGroupManager({
            timePeriods: CONFIG.TIME_PERIODS,
            groupConfigs: CONFIG.SESSION_GROUPS
        });

        if (this.isStorageAvailable) {
            // 加载聊天会话列表
            await this.loadChatSessions();

            // 恢复当前会话
            await this.restoreCurrentSession();
        }
    },

    methods: {
        // 创建功能卡片数据
        createFeatureCards() {
            const cardConfigs = [
                {
                    id: 'location-capability',
                    bgIndex: 1,
                    iconIndex: 1,
                    title: '位置能力使用推荐',
                    description:
                        '根据用户需求及业务口径，结合开放目录、区域洞察AP1、实时事件、平台指标，将匹配的能力以不同的组件形式向用户展示。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    bgIndex: 2,
                    iconIndex: 2,
                    title: '位置资产订购向导',
                    description:
                        '分析用户需求，引导用户订购推荐的数据资产(API/数据开放目录表等)，同时输出可视化操作步骤指引用户订购。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    bgIndex: 3,
                    iconIndex: 3,
                    title: '位置业务需求规格',
                    description:
                        '提取用户需求中的时空信息，包括业务背景、业务口径、数据账期地理范围，以格式化方式呈现，支持生成并下载需求规格说明书。',
                    action: 'business-data'
                }
            ];

            return cardConfigs.map((config) => ({
                ...config,
                bg: require(`@/img/main/card-bg-${config.bgIndex}.png`),
                icon: require(`@/img/main/card-logo-${config.iconIndex}.png`)
            }));
        },

        // 处理新对话
        async handleNewChat() {
            // 如果AI正在回复，禁用新建对话功能
            if (this.isAiResponseBlocked()) {
                return;
            }

            // 清理当前的临时会话（如果存在且为空）
            await this.cleanupEmptyTemporarySession();

            // 创建临时会话（不保存到存储）
            const tempSessionId = this.generateTempSessionId();
            this.setSessionState({
                id: tempSessionId,
                messages: [],
                model: this.selectedModel,
                isTemporary: true
            });

            this.currentView = CONFIG.VIEWS.HOME;
        },

        // 加载聊天会话列表
        async loadChatSessions() {
            if (!this.isStorageAvailable) return;

            try {
                this.chatSessions = chatStorageService.getAllSessions();
            } catch (error) {
                this.handleError(error, '加载聊天会话', false);
                this.chatSessions = [];
            }
        },

        // 恢复当前会话
        async restoreCurrentSession() {
            if (!this.isStorageAvailable) return;

            try {
                const currentSessionId = chatStorageService.getCurrentSessionId();
                if (!currentSessionId) return;

                // 检查是否为临时会话ID，如果是则不恢复
                if (currentSessionId.startsWith(CONFIG.TEMP_SESSION_PREFIX)) {
                    chatStorageService.setCurrentSessionId(null);
                    return;
                }

                const session = chatStorageService.getSession(currentSessionId);
                if (!session) return;

                // 使用通用方法设置会话状态
                this.setSessionState({
                    id: currentSessionId,
                    messages: session.messages,
                    model: session.model,
                    isTemporary: false
                });

                // 如果有消息，切换到聊天页面
                if (this.chatMessages.length > 0) {
                    this.currentView = CONFIG.VIEWS.CHAT;
                }
            } catch (error) {
                this.handleError(error, '恢复当前会话', false);
            }
        },

        // 按时间分组会话
        groupSessionsByTime(sessions) {
            if (!this.timeGroupManager) {
                return [];
            }
            return this.timeGroupManager.groupSessionsByTime(sessions);
        },

        // 创建时间范围
        createTimeRanges() {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            return {
                today: today,
                yesterday: new Date(today.getTime() - CONFIG.TIME_PERIODS.DAY),
                weekAgo: new Date(today.getTime() - CONFIG.TIME_PERIODS.WEEK),
                monthAgo: new Date(today.getTime() - CONFIG.TIME_PERIODS.MONTH)
            };
        },

        // 初始化分组
        initializeGroups() {
            const groups = {};
            Object.keys(CONFIG.SESSION_GROUPS).forEach((key) => {
                groups[key] = {
                    ...CONFIG.SESSION_GROUPS[key],
                    items: []
                };
            });
            return groups;
        },

        // 创建会话项
        createSessionItem(session) {
            return {
                id: session.id,
                text: session.title,
                type: 'conversation',
                timestamp: session.updatedAt,
                messageCount: session.messageCount || 0
            };
        },

        // 确定分组键
        determineGroupKey(sessionDate, timeRanges) {
            if (sessionDate >= timeRanges.today) {
                return 'today';
            }
            if (sessionDate >= timeRanges.yesterday) {
                return 'yesterday';
            }
            if (sessionDate >= timeRanges.weekAgo) {
                return 'week';
            }
            if (sessionDate >= timeRanges.monthAgo) {
                return 'month';
            }
            return null;
        },

        // 处理搜索选择
        async handleSearchSelect(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponseBlocked()) {
                return;
            }

            await this.switchToSession(item.id);
        },

        // 处理菜单项点击
        async handleMenuItemClick(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponseBlocked()) {
                return;
            }

            if (item.type === 'conversation') {
                await this.switchToSession(item.id);
            }
        },

        // 切换到指定会话
        async switchToSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 清理当前的临时会话（如果存在且为空）
                await this.cleanupEmptyTemporarySession();

                const session = chatStorageService.getSession(sessionId);
                if (session) {
                    // 使用通用方法设置会话状态
                    this.setSessionState({
                        id: sessionId,
                        messages: session.messages,
                        model: session.model,
                        isTemporary: false
                    });

                    this.currentView = CONFIG.VIEWS.CHAT;

                    // 更新当前会话ID
                    chatStorageService.setCurrentSessionId(sessionId);
                }
            } catch (error) {
                this.handleError(error, '切换会话', false);
            }
        },

        // 生成临时会话ID
        generateTempSessionId() {
            return (
                CONFIG.TEMP_SESSION_PREFIX +
                Date.now().toString(36) +
                Math.random().toString(36).substring(2)
            );
        },

        // 清理空的临时会话
        async cleanupEmptyTemporarySession() {
            if (this.isTemporarySession && this.chatMessages.length === 0) {
                // 如果当前是空的临时会话，清理状态
                this.currentSessionId = null;
                this.activeMenuItem = null;
                this.isTemporarySession = false;

                // 清除localStorage中的当前会话ID
                if (this.isStorageAvailable) {
                    chatStorageService.setCurrentSessionId(null);
                }
            }
        },

        // 处理删除会话
        async handleDeleteSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 删除会话
                const success = chatStorageService.deleteSession(sessionId);
                if (success) {
                    // 重新加载会话列表
                    await this.loadChatSessions();

                    // 如果删除的是当前会话，切换到首页
                    if (this.currentSessionId === sessionId) {
                        this.currentSessionId = null;
                        this.chatMessages = [];
                        this.activeMenuItem = null;
                        this.currentView = 'home';
                    }
                }
            } catch (error) {
                this.handleError(error, '删除会话');
            }
        },

        // 处理功能卡片点击
        async handleCardClick() {
            // 创建新会话并切换到聊天页面
            await this.handleNewChat();
        },

        // 处理发送消息
        async handleSendMessage(message) {
            if (this.isAiResponseBlocked()) {
                return;
            }

            await this.ensureCurrentSession(message.model);
            await this.processUserMessage(message);
            this.currentView = CONFIG.VIEWS.CHAT;
            await this.sendAiMessage(message);
        },

        // 处理用户消息
        async processUserMessage(message) {
            const userMessage = {
                ...message,
                type: CONFIG.MESSAGE_TYPES.USER
            };

            this.chatMessages.push(userMessage);
            await this.saveUserMessageToStorage(userMessage);
        },

        // 保存用户消息到存储
        async saveUserMessageToStorage(userMessage) {
            if (!this.isStorageAvailable || !this.currentSessionId) {
                return;
            }

            const saveSuccess = chatStorageService.addMessage(this.currentSessionId, userMessage);

            if (saveSuccess) {
                await this.reloadSessionsIfNeeded();
            } else {
                console.warn('保存用户消息失败');
            }
        },

        // 发送AI消息
        async sendAiMessage(message) {
            try {
                const aiResponse = await this.aiConversationManager.sendMessage(
                    message,
                    this.currentSessionId,
                    this.chatMessages
                );

                await this.handleAiResponse(aiResponse);
            } catch (error) {
                await this.handleAiError(error);
            }
        },

        // 处理AI响应
        async handleAiResponse(aiResponse) {
            // 只有非流式响应才需要手动添加到消息列表
            if (aiResponse) {
                this.chatMessages.push(aiResponse);
                this.saveMessageToStorage(aiResponse);
            }
        },

        // 处理AI错误
        async handleAiError(error) {
            console.error('AI消息发送失败:', error);

            // 清理可能的空消息
            this.aiConversationManager.cleanupEmptyAiMessage(
                this.chatMessages,
                this.currentSessionId
            );

            // 如果不是用户取消，添加错误消息
            if (!error || !error.isCancelled) {
                await this.addErrorMessage(error);
            }
        },

        // 添加错误消息
        async addErrorMessage(error) {
            const errorMessage = this.getErrorMessage(error);
            const errorResponse = {
                text: `抱歉，服务暂时不可用。错误信息：${errorMessage}`,
                type: CONFIG.MESSAGE_TYPES.ASSISTANT,
                timestamp: new Date().toISOString(),
                isError: true
            };

            this.chatMessages.push(errorResponse);
            this.saveMessageToStorage(errorResponse);
        },

        // 处理停止生成
        handleStopGeneration() {
            if (!this.aiConversationManager) {
                return;
            }

            const stopped = this.aiConversationManager.stopGeneration();
            if (!stopped) {
                return;
            }

            // 处理被停止的AI消息
            const result = this.aiConversationManager.handleStoppedAiMessage(
                this.chatMessages,
                this.currentSessionId
            );

            if (!result.success) {
                return;
            }

            // 处理被移除的消息
            this.handleRemovedMessage(result.processedMessage);

            // 添加停止消息到本地消息列表
            if (result.stopMessage) {
                this.chatMessages.push(result.stopMessage);
            }

            // 重新加载会话列表以更新侧边栏
            if (this.isStorageAvailable) {
                this.loadChatSessions();
            }
        },

        // 处理被移除的消息
        handleRemovedMessage(processedMessage) {
            if (!processedMessage || processedMessage.action !== 'removed') {
                return;
            }

            const messageIndex = this.chatMessages.findIndex(
                (msg) => msg === processedMessage.message
            );
            if (messageIndex !== -1) {
                this.chatMessages.splice(messageIndex, 1);
            }
        },

        // AI状态变化回调
        handleAiStateChange() {
            // 强制更新组件以反映状态变化
            this.$forceUpdate();
        },

        // AI消息更新回调
        handleAiMessageUpdate(message, action) {
            // 消息动作处理映射
            const actionHandlers = {
                [CONFIG.MESSAGE_ACTIONS.ADD]: () => {
                    this.chatMessages.push(message);
                },
                [CONFIG.MESSAGE_ACTIONS.COMPLETE]: () => {
                    this.saveMessageToStorage(message);
                },
                [CONFIG.MESSAGE_ACTIONS.UPDATE]: () => {
                    // 流式更新处理
                },
                [CONFIG.MESSAGE_ACTIONS.STOPPED]: () => {
                    // 消息被停止处理
                },
                [CONFIG.MESSAGE_ACTIONS.ERROR]: () => {
                    // 消息错误处理
                }
            };

            const handler = actionHandlers[action];
            if (handler) {
                handler();
                this.forceComponentUpdate();
            }
        },

        // 保存消息到存储
        saveMessageToStorage(message) {
            if (this.isStorageAvailable && this.currentSessionId) {
                chatStorageService.addMessage(this.currentSessionId, message);
                this.loadChatSessions();
            }
        },

        // 强制组件更新
        forceComponentUpdate() {
            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },

        // 设置会话状态的通用方法
        setSessionState(sessionData) {
            this.currentSessionId = sessionData.id;
            this.chatMessages = sessionData.messages || [];
            this.selectedModel = this.getValidModel(sessionData.model);
            this.activeMenuItem = sessionData.id;
            this.isTemporarySession = sessionData.isTemporary || false;
        },

        // 获取有效的模型
        getValidModel(model) {
            const isValidModel = this.modelOptions.find((m) => m.value === model);
            if (isValidModel) {
                return model;
            }
            return this.defaultModel;
        },

        // 重新加载会话列表的通用方法
        async reloadSessionsIfNeeded() {
            if (this.isStorageAvailable) {
                await this.loadChatSessions();
            }
        },

        // 处理AI响应阻塞检查
        isAiResponseBlocked() {
            return this.isAiResponding;
        },

        // 统一错误处理
        handleError(error, context = '', showMessage = true) {
            const errorMessage = this.getErrorMessage(error);
            console.error(`${context}失败:`, error);

            if (showMessage && this.$message) {
                this.$message.error(errorMessage);
            }

            return errorMessage;
        },

        // 获取错误消息
        getErrorMessage(error) {
            if (!error) {
                return '未知错误';
            }

            if (typeof error === 'string') {
                return error;
            }

            if (error.errorMessage) {
                return error.errorMessage;
            }

            if (error.message) {
                return error.message;
            }

            return '未知错误';
        },

        // AI会话更新回调
        handleAiSessionUpdate() {
            // 重新加载会话列表以更新侧边栏
            if (this.isStorageAvailable) {
                this.loadChatSessions();
            }
        },

        // 确保有当前会话
        async ensureCurrentSession(model) {
            if (!this.isStorageAvailable) return;

            if (this.needsNewSession()) {
                await this.createNewSession(model);
            }
        },

        // 检查是否需要新会话
        needsNewSession() {
            return !this.currentSessionId || this.isTemporarySession;
        },

        // 创建新会话
        async createNewSession(model) {
            const sessionModel = model || this.selectedModel;
            const newSession = chatStorageService.createSession({ model: sessionModel });

            this.setSessionState({
                id: newSession.id,
                messages: this.chatMessages,
                model: sessionModel,
                isTemporary: false
            });

            await this.reloadSessionsIfNeeded();
        },

        // 处理模型变更
        async handleModelChange(newModel) {
            this.selectedModel = newModel;

            // 使用AI会话管理器更改模型
            if (this.aiConversationManager) {
                await this.aiConversationManager.changeModel(newModel, this.currentSessionId);
            }
        },

        // 处理会话标题更新
        async handleUpdateSessionTitle(sessionId, newTitle) {
            if (!this.validateSessionTitleUpdate(sessionId, newTitle)) {
                return;
            }

            try {
                await this.updateSessionTitle(sessionId, newTitle);
            } catch (error) {
                this.handleSessionTitleUpdateError(error);
            }
        },

        // 验证会话标题更新
        validateSessionTitleUpdate(sessionId, newTitle) {
            if (!this.isStorageAvailable || !sessionId || !newTitle) {
                return false;
            }

            // 不允许更新临时会话的标题
            if (sessionId.startsWith(CONFIG.TEMP_SESSION_PREFIX)) {
                console.warn('不能更新临时会话的标题');
                return false;
            }

            return true;
        },

        // 更新会话标题
        async updateSessionTitle(sessionId, newTitle) {
            const success = chatStorageService.updateSessionTitle(sessionId, newTitle);
            if (success) {
                await this.reloadSessionsIfNeeded();
            }
        },

        // 处理会话标题更新错误
        handleSessionTitleUpdateError(error) {
            this.handleError(error, '更新会话标题');
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100vh;
}
</style>
