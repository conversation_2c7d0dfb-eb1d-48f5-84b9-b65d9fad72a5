import SessionTimeGroupManager from '../SessionTimeGroupManager.js';

describe('SessionTimeGroupManager', () => {
    let manager;
    let mockSessions;

    beforeEach(() => {
        manager = new SessionTimeGroupManager();
        
        // 模拟会话数据
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        
        mockSessions = [
            {
                id: '1',
                title: '今天的会话',
                updatedAt: today.toISOString(),
                messageCount: 5
            },
            {
                id: '2', 
                title: '昨天的会话',
                updatedAt: new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString(),
                messageCount: 3
            },
            {
                id: '3',
                title: '一周前的会话', 
                updatedAt: new Date(today.getTime() - 8 * 24 * 60 * 60 * 1000).toISOString(),
                messageCount: 2
            }
        ];
    });

    describe('groupSessionsByTime', () => {
        test('应该正确按时间分组会话', () => {
            const result = manager.groupSessionsByTime(mockSessions);
            
            expect(result).toHaveLength(3); // 今天、昨天、一周内
            
            const todayGroup = result.find(group => group.key === 'today');
            expect(todayGroup.items).toHaveLength(1);
            expect(todayGroup.items[0].text).toBe('今天的会话');
            
            const yesterdayGroup = result.find(group => group.key === 'yesterday');
            expect(yesterdayGroup.items).toHaveLength(1);
            expect(yesterdayGroup.items[0].text).toBe('昨天的会话');
        });

        test('应该处理空会话列表', () => {
            const result = manager.groupSessionsByTime([]);
            expect(result).toEqual([]);
        });

        test('应该处理无效输入', () => {
            const result = manager.groupSessionsByTime(null);
            expect(result).toEqual([]);
        });
    });

    describe('createTimeRanges', () => {
        test('应该创建正确的时间范围', () => {
            const baseDate = new Date('2024-01-15T10:00:00Z');
            const ranges = manager.createTimeRanges(baseDate);
            
            expect(ranges.today).toEqual(new Date('2024-01-15T00:00:00Z'));
            expect(ranges.yesterday).toEqual(new Date('2024-01-14T00:00:00Z'));
        });
    });

    describe('determineGroupKey', () => {
        test('应该正确确定分组键', () => {
            const baseDate = new Date('2024-01-15T10:00:00Z');
            const ranges = manager.createTimeRanges(baseDate);
            
            // 今天
            const todayDate = new Date('2024-01-15T15:00:00Z');
            expect(manager.determineGroupKey(todayDate, ranges)).toBe('today');
            
            // 昨天
            const yesterdayDate = new Date('2024-01-14T15:00:00Z');
            expect(manager.determineGroupKey(yesterdayDate, ranges)).toBe('yesterday');
            
            // 无效日期
            expect(manager.determineGroupKey(new Date('invalid'), ranges)).toBeNull();
        });
    });

    describe('getRelativeTimeDescription', () => {
        test('应该返回正确的相对时间描述', () => {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            
            expect(manager.getRelativeTimeDescription(today)).toBe('今天');
            
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            expect(manager.getRelativeTimeDescription(yesterday)).toBe('昨天');
            
            const veryOld = new Date('2020-01-01');
            expect(manager.getRelativeTimeDescription(veryOld)).toBe('更早');
        });
    });

    describe('getGroupStatistics', () => {
        test('应该返回正确的统计信息', () => {
            const stats = manager.getGroupStatistics(mockSessions);
            
            expect(stats.today).toEqual({
                title: '今天',
                count: 1,
                totalMessages: 5
            });
            
            expect(stats.yesterday).toEqual({
                title: '昨天', 
                count: 1,
                totalMessages: 3
            });
        });
    });

    describe('配置更新', () => {
        test('应该能够更新分组配置', () => {
            manager.updateGroupConfigs({
                today: { key: 'today', title: 'Today' }
            });
            
            const result = manager.groupSessionsByTime(mockSessions);
            const todayGroup = result.find(group => group.key === 'today');
            expect(todayGroup.title).toBe('Today');
        });

        test('应该能够更新时间周期配置', () => {
            manager.updateTimePeriods({
                DAY: 12 * 60 * 60 * 1000 // 12小时
            });
            
            // 验证新配置生效
            expect(manager.timePeriods.DAY).toBe(12 * 60 * 60 * 1000);
        });
    });
});
