/**
 * 会话时间分组管理器
 * 负责处理会话按时间分组的相关逻辑
 */
class SessionTimeGroupManager {
    constructor(options = {}) {
        // 时间周期配置（毫秒）
        this.timePeriods = {
            DAY: 24 * 60 * 60 * 1000,
            WEEK: 7 * 24 * 60 * 60 * 1000,
            MONTH: 30 * 24 * 60 * 60 * 1000,
            ...options.timePeriods
        };

        // 分组配置
        this.groupConfigs = {
            today: { key: 'today', title: '今天' },
            yesterday: { key: 'yesterday', title: '昨天' },
            week: { key: 'week', title: '一周内' },
            month: { key: 'month', title: '30天内' },
            ...options.groupConfigs
        };
    }

    /**
     * 按时间分组会话
     * @param {Array} sessions - 会话列表
     * @returns {Array} 分组后的会话列表
     */
    groupSessionsByTime(sessions) {
        if (!Array.isArray(sessions) || sessions.length === 0) {
            return [];
        }

        const timeRanges = this.createTimeRanges();
        const groups = this.initializeGroups();

        sessions.forEach((session) => {
            const sessionDate = new Date(session.updatedAt);
            const item = this.createSessionItem(session);
            const groupKey = this.determineGroupKey(sessionDate, timeRanges);
            
            if (groupKey && groups[groupKey]) {
                groups[groupKey].items.push(item);
            }
        });

        // 只返回有内容的分组
        return Object.values(groups).filter((group) => group.items.length > 0);
    }

    /**
     * 创建时间范围
     * @param {Date} baseDate - 基准日期，默认为当前时间
     * @returns {Object} 时间范围对象
     */
    createTimeRanges(baseDate = new Date()) {
        const today = new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate());
        
        return {
            today: today,
            yesterday: new Date(today.getTime() - this.timePeriods.DAY),
            weekAgo: new Date(today.getTime() - this.timePeriods.WEEK),
            monthAgo: new Date(today.getTime() - this.timePeriods.MONTH)
        };
    }

    /**
     * 初始化分组结构
     * @returns {Object} 初始化的分组对象
     */
    initializeGroups() {
        const groups = {};
        Object.keys(this.groupConfigs).forEach((key) => {
            groups[key] = {
                ...this.groupConfigs[key],
                items: []
            };
        });
        return groups;
    }

    /**
     * 创建会话项
     * @param {Object} session - 原始会话对象
     * @returns {Object} 格式化的会话项
     */
    createSessionItem(session) {
        return {
            id: session.id,
            text: session.title,
            type: 'conversation',
            timestamp: session.updatedAt,
            messageCount: session.messageCount || 0
        };
    }

    /**
     * 确定会话所属的时间分组
     * @param {Date} sessionDate - 会话日期
     * @param {Object} timeRanges - 时间范围对象
     * @returns {string|null} 分组键名
     */
    determineGroupKey(sessionDate, timeRanges) {
        if (!(sessionDate instanceof Date) || isNaN(sessionDate.getTime())) {
            return null;
        }

        if (sessionDate >= timeRanges.today) {
            return 'today';
        }
        if (sessionDate >= timeRanges.yesterday) {
            return 'yesterday';
        }
        if (sessionDate >= timeRanges.weekAgo) {
            return 'week';
        }
        if (sessionDate >= timeRanges.monthAgo) {
            return 'month';
        }
        return null;
    }

    /**
     * 获取会话的相对时间描述
     * @param {string|Date} timestamp - 时间戳或日期对象
     * @returns {string} 相对时间描述
     */
    getRelativeTimeDescription(timestamp) {
        const sessionDate = new Date(timestamp);
        const timeRanges = this.createTimeRanges();
        const groupKey = this.determineGroupKey(sessionDate, timeRanges);
        
        if (groupKey && this.groupConfigs[groupKey]) {
            return this.groupConfigs[groupKey].title;
        }
        
        return '更早';
    }

    /**
     * 检查两个日期是否在同一天
     * @param {Date} date1 - 第一个日期
     * @param {Date} date2 - 第二个日期
     * @returns {boolean} 是否在同一天
     */
    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    /**
     * 获取时间分组的统计信息
     * @param {Array} sessions - 会话列表
     * @returns {Object} 统计信息
     */
    getGroupStatistics(sessions) {
        const groups = this.groupSessionsByTime(sessions);
        const stats = {};
        
        groups.forEach(group => {
            stats[group.key] = {
                title: group.title,
                count: group.items.length,
                totalMessages: group.items.reduce((sum, item) => sum + item.messageCount, 0)
            };
        });
        
        return stats;
    }

    /**
     * 更新分组配置
     * @param {Object} newConfigs - 新的分组配置
     */
    updateGroupConfigs(newConfigs) {
        this.groupConfigs = { ...this.groupConfigs, ...newConfigs };
    }

    /**
     * 更新时间周期配置
     * @param {Object} newPeriods - 新的时间周期配置
     */
    updateTimePeriods(newPeriods) {
        this.timePeriods = { ...this.timePeriods, ...newPeriods };
    }
}

export default SessionTimeGroupManager;
