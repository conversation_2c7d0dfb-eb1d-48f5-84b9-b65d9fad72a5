/* eslint-disable max-depth */
/* eslint-disable complexity */
import axios from 'axios';

/**
 * AI会话管理器
 * 负责处理AI对话相关的所有操作，包括消息发送、流式响应、模型管理等
 */
class AIConversationManager {
    constructor(options = {}) {
        // 必需的依赖
        this.assistantApi = options.assistantApi;
        this.chatStorageService = options.chatStorageService;
        this.modelOptions = options.modelOptions || [];

        // 回调函数
        this.onStateChange = options.onStateChange || (() => { });
        this.onMessageUpdate = options.onMessageUpdate || (() => { });
        this.onSessionUpdate = options.onSessionUpdate || (() => { });

        // 内部状态
        this.isAiResponding = false;
        this.isStreamResponse = false;
        this.currentCancelTokenSource = null;
        this.selectedModel = options.defaultModel || 'DifyStream';
    }

    /**
     * 发送消息并获取AI回复
     * @param {Object} message - 消息对象
     * @param {string} sessionId - 会话ID
     * @param {Array} messages - 消息历史
     * @returns {Promise<Object>} 返回AI回复消息
     */
    async sendMessage(message, sessionId, messages) {
        if (this.isAiResponding) {
            throw new Error('AI正在回复中，请稍后再试');
        }

        // 设置AI回复状态
        this._updateState({ isAiResponding: true });

        // 创建CancelToken用于取消请求
        this.currentCancelTokenSource = axios.CancelToken.source();

        try {
            // 构建聊天历史
            const chatHistory = messages
                .filter((msg) => msg.type === 'user' || msg.type === 'assistant')
                .map((msg) => ({
                    role: msg.type === 'user' ? 'user' : 'assistant',
                    content: msg.text
                }));

            // 根据选择的模型调用对应的服务
            const apiParams = {
                model: this.getModelName(message.model),
                messages: chatHistory,
                max_tokens: 2048,
                temperature: 0.7
            };

            const service = this.getServiceByModel(message.model);
            const cancelToken = this.currentCancelTokenSource.token;
            const isStream = this.isStreamModel(message.model);

            let aiResponse;

            if (isStream) {
                // 流式响应处理
                this._updateState({ isStreamResponse: true });
                aiResponse = await this._handleStreamResponse(service, apiParams, cancelToken, message);
                // 流式响应的消息已经通过回调添加到组件中，这里返回 null
                return null;
            }
            // 传统响应处理
            this._updateState({ isStreamResponse: false });
            const response = await this._callTraditionalApi(service, apiParams, cancelToken);

            if (response && response.choices && response.choices.length > 0) {
                const responseContent = response.choices[0].message.content;

                if (!responseContent || responseContent.trim() === '') {
                    throw new Error('API 返回空内容');
                }

                aiResponse = {
                    text: responseContent,
                    type: 'assistant',
                    timestamp: new Date().toISOString(),
                    model: message.model
                };
            } else {
                throw new Error('API 响应格式错误');
            }


            return aiResponse;

        } catch (error) {
            console.error('API 调用失败:', error);

            // 检查是否是用户主动取消的请求
            if (error && error.isCancelled) {
                throw error;
            }

            // 处理其他错误
            let errorMessage = '未知错误';
            if (error && typeof error === 'object') {
                if (error.errorMessage) {
                    errorMessage = error.errorMessage;
                } else if (error.message) {
                    errorMessage = error.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }
            }

            const errorResponse = {
                text: `抱歉，服务暂时不可用。错误信息：${errorMessage}`,
                type: 'assistant',
                timestamp: new Date().toISOString(),
                isError: true
            };

            return errorResponse;

        } finally {
            // 重置AI回复状态
            this._updateState({
                isAiResponding: false,
                isStreamResponse: false
            });
            this.currentCancelTokenSource = null;
        }
    }

    /**
     * 停止AI生成
     */
    stopGeneration() {
        if (this.currentCancelTokenSource && (this.isAiResponding || this.isStreamResponse)) {
            // 取消当前请求
            this.currentCancelTokenSource.cancel('用户停止了AI回复生成');

            // 重置状态
            this._updateState({
                isAiResponding: false,
                isStreamResponse: false
            });
            this.currentCancelTokenSource = null;

            return true;
        }
        return false;
    }

    /**
     * 更改模型
     * @param {string} newModel - 新模型
     * @param {string} sessionId - 会话ID
     */
    async changeModel(newModel, sessionId) {
        this.selectedModel = newModel;

        // 如果有当前会话，更新会话的模型
        if (this.chatStorageService && sessionId) {
            this.chatStorageService.saveSession(sessionId, { model: newModel });
        }

        this.onSessionUpdate(sessionId);
    }

    /**
     * 根据模型获取对应的服务
     * @param {string} model - 模型名称
     * @returns {string} 服务名称
     */
    getServiceByModel(model) {
        const serviceMap = {
            DeepSeek: 'deepseek',
            Dify: 'dify',
            'OpenAI/vLLM': 'openai',
            DifyStream: 'dify'
        };
        return serviceMap[model] || 'deepseek';
    }

    /**
     * 检查是否为流式模型
     * @param {string} model - 模型名称
     * @returns {boolean} 是否为流式模型
     */
    isStreamModel(model) {
        return model === 'DifyStream';
    }

    /**
     * 获取实际的模型名称
     * @param {string} model - 模型名称
     * @returns {string} 实际模型名称
     */
    getModelName(model) {
        const modelMap = {
            DeepSeek: 'deepseek-chat',
            Dify: 'dify-chatflow',
            'OpenAI/vLLM': 'qwen3-30b-a3b',
            DifyStream: 'dify-chatflow'
        };
        return modelMap[model] || 'deepseek-chat';
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return {
            isAiResponding: this.isAiResponding,
            isStreamResponse: this.isStreamResponse,
            selectedModel: this.selectedModel,
            hasActiveRequest: !!this.currentCancelTokenSource
        };
    }

    /**
     * 处理被停止的AI消息
     * @param {Array} messages - 消息列表
     * @param {string} sessionId - 会话ID
     * @returns {Object} 停止操作的结果
     */
    handleStoppedAiMessage(messages, sessionId) {
        try {
            let processedMessage = null;

            // 查找最后一条AI消息
            for (let i = messages.length - 1; i >= 0; i--) {
                const message = messages[i];
                if (message.type === 'assistant') {
                    // 检查消息内容是否为空或只包含空白字符
                    if (!message.text || message.text.trim() === '') {
                        console.log('清理空的AI消息:', message);

                        // 从存储中移除（如果消息有ID）
                        if (this.chatStorageService && sessionId && message.id) {
                            this.chatStorageService.removeMessage(sessionId, message.id);
                        } else if (this.chatStorageService && sessionId) {
                            // 如果没有ID，尝试移除最后一条消息
                            this.chatStorageService.removeLastMessage(sessionId);
                        }

                        processedMessage = { action: 'removed', message };
                    } else {
                        // 如果有内容，标记为被停止的消息并保存
                        console.log('保存被停止的AI消息:', message);

                        // 标记消息为被停止状态
                        message.isStreaming = false;
                        message.isStopped = true;

                        // 保存到存储
                        if (this.chatStorageService && sessionId) {
                            this.chatStorageService.addMessage(sessionId, message);
                        }

                        processedMessage = { action: 'stopped', message };
                    }
                    break; // 只处理最后一条AI消息
                }
            }

            // 创建停止操作的系统消息
            const stopMessage = {
                text: '[用户停止了AI回复生成]',
                type: 'system',
                timestamp: new Date().toISOString(),
                isStopAction: true
            };

            // 保存停止消息到存储
            if (this.chatStorageService && sessionId) {
                this.chatStorageService.addMessage(sessionId, stopMessage);
            }

            this.onSessionUpdate(sessionId);

            return {
                processedMessage,
                stopMessage,
                success: true
            };

        } catch (error) {
            console.error('处理被停止的AI消息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 清理空的AI消息
     * @param {Array} messages - 消息列表
     * @param {string} sessionId - 会话ID
     * @returns {boolean} 是否清理了消息
     */
    cleanupEmptyAiMessage(messages, sessionId) {
        try {
            // 查找最后一条AI消息
            for (let i = messages.length - 1; i >= 0; i--) {
                const message = messages[i];
                if (message.type === 'assistant') {
                    // 检查消息内容是否为空或只包含空白字符
                    if (!message.text || message.text.trim() === '') {
                        console.log('清理空的AI消息:', message);

                        // 从存储中移除（如果消息有ID）
                        if (this.chatStorageService && sessionId && message.id) {
                            this.chatStorageService.removeMessage(sessionId, message.id);
                        } else if (this.chatStorageService && sessionId) {
                            // 如果没有ID，尝试移除最后一条消息
                            this.chatStorageService.removeLastMessage(sessionId);
                        }

                        return true;
                    }
                    break; // 只处理最后一条AI消息
                }
            }
            return false;
        } catch (error) {
            console.error('清理空AI消息失败:', error);
            return false;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 调用传统API
     * @private
     * @param {string} service - 服务名称
     * @param {Object} apiParams - API参数
     * @param {Object} cancelToken - 取消令牌
     * @returns {Promise<Object>} API响应
     */
    async _callTraditionalApi(service, apiParams, cancelToken) {
        let response;

        if (service === 'deepseek') {
            response = await this.assistantApi.deepseekChat(apiParams, cancelToken);
        } else if (service === 'dify') {
            apiParams.parameters = {
                industry: '位置大数据',
                analysis_type: 'general',
                user_id: 'user_001'
            };
            response = await this.assistantApi.difyChat(apiParams, cancelToken);
        } else if (service === 'openai') {
            response = await this.assistantApi.openaiChat(apiParams, cancelToken);
        } else {
            // 默认使用 DeepSeek
            response = await this.assistantApi.defaultChat(apiParams, cancelToken);
        }

        return response;
    }

    /**
     * 处理流式响应
     * @private
     * @param {string} service - 服务名称
     * @param {Object} apiParams - API参数
     * @param {Object} cancelToken - 取消令牌
     * @param {Object} message - 原始消息
     * @returns {Promise<Object>} 流式响应消息
     */
    async _handleStreamResponse(service, apiParams, cancelToken, message) {
        return new Promise((resolve, reject) => {
            // 创建流式响应的消息对象
            const streamMessage = {
                id: this._generateMessageId(),
                text: '',
                type: 'assistant',
                timestamp: new Date().toISOString(),
                model: message.model,
                isStreaming: true
            };

            // 通过回调添加消息到组件
            this.onMessageUpdate(streamMessage, 'add');

            // 为 Dify 服务添加特殊参数
            if (service === 'dify') {
                apiParams.parameters = {
                    industry: '位置大数据',
                    analysis_type: 'general',
                    user_id: 'user_001'
                };
            }

            // 流式数据处理回调
            const onData = (data) => {
                let content = '';

                // 处理不同格式的流式数据
                if (data.text) {
                    // Dify 流式格式（从 answer 字段提取的文本）
                    content = data.text;
                } else if (data.choices && data.choices.length > 0) {
                    // OpenAI 格式（兼容性保留）
                    const delta = data.choices[0].delta;
                    if (delta && delta.content) {
                        content = delta.content;
                    }
                } else if (data.content) {
                    // 直接内容格式
                    content = data.content;
                } else if (typeof data === 'string') {
                    // 纯字符串格式
                    content = data;
                }

                // 更新消息内容
                if (content) {
                    // 第一个数据包到达时立即隐藏加载状态，但保持流式状态
                    if (this.isAiResponding) {
                        this._updateState({
                            isAiResponding: false,
                            isStreamResponse: true
                        });
                    }

                    // 更新消息文本
                    streamMessage.text = (streamMessage.text || '') + content;

                    // 触发消息更新回调
                    this.onMessageUpdate(streamMessage, 'update');
                }
            };

            // 流式完成回调
            const onComplete = () => {
                // 标记流式响应完成
                streamMessage.isStreaming = false;

                // 检查流式消息是否为空
                if (!streamMessage.text || streamMessage.text.trim() === '') {
                    reject(new Error('流式响应内容为空'));
                    return;
                }

                // 触发消息完成回调
                this.onMessageUpdate(streamMessage, 'complete');

                resolve(streamMessage);
            };

            // 流式错误回调
            const onError = (error) => {
                if (error.isCancelled) {
                    // 如果是用户取消，标记消息状态
                    if (!streamMessage.text || streamMessage.text.trim() === '') {
                        // 空消息直接拒绝
                        reject(error);
                    } else {
                        // 有内容的消息标记为被停止
                        streamMessage.isStreaming = false;
                        streamMessage.isStopped = true;
                        this.onMessageUpdate(streamMessage, 'stopped');
                        resolve(streamMessage);
                    }
                    return;
                }

                console.error('流式响应错误:', error);

                // 标记消息为错误状态
                streamMessage.isError = true;
                streamMessage.isStreaming = false;

                // 如果没有接收到任何内容，显示错误信息
                if (!streamMessage.text || streamMessage.text.trim() === '') {
                    streamMessage.text = '抱歉，生成回复时出现错误，请重试。';
                }

                this.onMessageUpdate(streamMessage, 'error');
                resolve(streamMessage);
            };

            // 调用对应的流式API（仅支持 Dify）
            if (service === 'dify') {
                this.assistantApi.difyStreamChatFlow(
                    apiParams,
                    cancelToken,
                    onData,
                    onComplete,
                    onError
                ).catch(onError);
            } else {
                // 其他服务暂不支持流式响应
                reject(new Error(`服务 ${service} 暂不支持流式响应`));
            }
        });
    }

    /**
     * 生成唯一消息ID
     * @private
     * @returns {string} 唯一ID
     */
    _generateMessageId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * 更新内部状态并触发回调
     * @private
     * @param {Object} newState - 新状态
     */
    _updateState(newState) {
        const oldState = this.getState();

        // 更新状态
        Object.assign(this, newState);

        // 触发状态变化回调
        this.onStateChange(this.getState(), oldState);
    }
}

export default AIConversationManager;
